import axios from 'axios';
import { BillingInterval, SubscriptionRequest, SubscriptionResponse, UserData, SubscriptionPlan, RedirectUrls } from '@/types/subscription';

// Create an axios instance with default config
const api = axios.create({
  baseURL: 'http://localhost:8084',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'Xz63TtCHPGLacYdsRVn9hfA4tdSZZbK07mgYG2yY',
  },
});

export const subscribeUser = async (
  userData: UserData,
  planName: string,
  price: number,
  billingInterval: BillingInterval,
  baseUrl: string = typeof window !== 'undefined' ? window.location.origin : ''
): Promise<SubscriptionResponse> => {
  const externalId = `user_${Date.now()}`;

  const systemPlan = planName.toLowerCase();

  const endDate = new Date();
  endDate.setFullYear(endDate.getFullYear() + 1);

  // Create subscription plan
  const subscriptionPlan: SubscriptionPlan = {
    externalId,
    systemSubscriptionPlan: systemPlan,
    endDate: endDate.toISOString(),
    price,
    billingInterval,
  };

  // Create redirect URLs
  const redirectUrls: RedirectUrls = {
    successUrl: `${baseUrl}/payment/success`,
    failureUrl: `${baseUrl}/payment/failure`,
    baseUrl,
  };

  // Create request payload
  const requestPayload: SubscriptionRequest = {
    user_data: userData,
    subscription_plan: subscriptionPlan,
    redirect_urls: redirectUrls,
  };

  try {
    const response = await api.post<SubscriptionResponse>('/end-users/subscribe', requestPayload);
    return response.data;
  } catch (error) {
    console.error('Error subscribing user:', error);
    throw error;
  }
};
