import Container from '../ui/Container';
import Button from '../ui/Button';

export default function GymHero() {
  return (
    <div id="hero" className="bg-gradient-to-r from-red-50 to-orange-50 py-16 sm:py-24">
      <Container>
        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          <div className="sm:text-center md:mx-auto md:max-w-2xl lg:col-span-6 lg:text-left">
            <h1>
              <span className="block text-base font-semibold text-red-600 sm:text-lg lg:text-base xl:text-lg">
                Bienvenue chez AtlasFit
              </span>
              <span className="mt-1 block text-4xl font-bold tracking-tight sm:text-5xl xl:text-6xl">
                <span className="block text-gray-900">Votre Salle de Sport</span>
                <span className="block text-red-600">Premium au Maroc</span>
              </span>
            </h1>
            <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">
              Découvrez notre salle de sport moderne avec des équipements de pointe, des coachs professionnels et des programmes
              d'entraînement personnalisés pour tous les niveaux.
            </p>
            <div className="mt-8 sm:mx-auto sm:max-w-lg sm:text-center lg:mx-0 lg:text-left">
              <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                <Button href="/register" size="lg" className="w-full sm:w-auto bg-red-600 hover:bg-red-700">
                  Essai Gratuit
                </Button>
                <Button href="/pricing" variant="outline" size="lg" className="w-full sm:w-auto border-red-600 text-red-600 hover:bg-red-50">
                  Voir les Abonnements
                </Button>
              </div>
              <p className="mt-3 text-sm text-gray-500">
                Profitez d'une séance d'essai gratuite et de -20% sur votre premier mois d'abonnement.
              </p>
            </div>
          </div>
          <div className="relative mt-12 sm:mx-auto sm:max-w-lg lg:col-span-6 lg:mx-0 lg:mt-0 lg:flex lg:max-w-none lg:items-center">
            <div className="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
              <div className="relative block w-full overflow-hidden rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                <img
                  className="w-full"
                  src="https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Salle de sport moderne"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    type="button"
                    className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  >
                    <svg
                      className="h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span className="sr-only">Regarder la vidéo</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
