import React from 'react';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import Container from '@/components/ui/Container';
import AuthBackground from './AuthBackground';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  description: string;
}

export default function AuthLayout({ children, title, description }: AuthLayoutProps) {
  return (
    <>
      <Head>
        <title>{`${title} | AtlasFit - Salle de Sport Premium au Maroc`}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Layout>
        <AuthBackground>
          <Container>
            <div className="flex flex-col items-center justify-center">
              {children}
            </div>
          </Container>
        </AuthBackground>
      </Layout>
    </>
  );
}
