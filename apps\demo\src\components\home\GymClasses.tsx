import React from 'react'
import Container from '../ui/Container'
import Button from '../ui/Button'

const classes = [
  {
    name: "Zumba",
    description: "Dan<PERSON>z et brûlez des calories avec notre cours de Zumba énergique.",
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
    duration: "60 min",
    intensity: "Modérée",
    schedule: "Lun, Mer, Ven - 18h"
  },
  {
    name: "Spinning",
    description: "Pédalez au rythme de la musique dans notre salle de cycling équipée des derniers vélos.",
    image: "https://images.unsplash.com/photo-1534787238916-9ba6764efd4f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
    duration: "45 min",
    intensity: "Intense",
    schedule: "Mar, Jeu - 19h, Sam - 10h"
  },
  {
    name: "Yoga",
    description: "Retrouvez calme et flexibilité avec nos cours de yoga adaptés à tous les niveaux.",
    image: "https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
    duration: "75 min",
    intensity: "Douce",
    schedule: "Lun, Mer, Ven - 8h, Mar, Jeu - 20h"
  },
  {
    name: "Boxe",
    description: "Apprenez les techniques de boxe et améliorez votre condition physique.",
    image: "https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
    duration: "60 min",
    intensity: "Très intense",
    schedule: "Mar, Jeu - 18h, Sam - 11h"
  },
]

const GymClasses = () => {
  return (
    <div id="classes" className="bg-gray-50 py-16 sm:py-24">
      <Container>
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Nos Cours Collectifs
          </h2>
          <p className="mt-4 text-lg text-gray-500">
            Découvrez notre large gamme de cours collectifs pour tous les niveaux et tous les objectifs.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-12 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-4">
          {classes.map((gymClass, index) => (
            <div key={index} className="group relative overflow-hidden rounded-xl bg-white shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-1 flex flex-col h-[500px]">
              <div className="aspect-h-2 aspect-w-3 overflow-hidden max-h-[200px]">
                <img
                  src={gymClass.image}
                  alt={gymClass.name}
                  className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <div className="absolute top-4 right-4">
                <span className="rounded-full bg-red-600 px-3 py-1.5 text-xs font-semibold text-white shadow-lg">
                  {gymClass.duration}
                </span>
              </div>

              <div className="p-6 relative z-10 flex-grow flex flex-col">
                <h3 className="text-xl font-bold text-gray-900 group-hover:text-red-600 transition-colors duration-300">{gymClass.name}</h3>
                <p className="mt-3 text-sm text-gray-600 line-clamp-2 h-10">{gymClass.description}</p>

                <div className="mt-4 flex items-center gap-2">
                  <div className="flex items-center gap-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium text-gray-700">{gymClass.intensity}</span>
                  </div>
                </div>

                <div className="mt-auto pt-3 border-t border-gray-100">
                  <div className="flex items-center gap-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    <span className="text-xs text-gray-600">{gymClass.schedule}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button href="/programs" className="bg-red-600 hover:bg-red-700">
            Voir tous nos cours
          </Button>
        </div>
      </Container>
    </div>
  )
}

export default GymClasses
