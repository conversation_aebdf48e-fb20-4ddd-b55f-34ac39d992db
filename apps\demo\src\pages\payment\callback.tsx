import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import WebhookHandler from '@/components/payment/WebhookHandler';
import { WebhookData } from '@/types/subscription';

export default function PaymentCallback() {
  const router = useRouter();
  const [webhookData, setWebhookData] = useState<WebhookData | null>(null);
  
  useEffect(() => {
    // Wait for router to be ready
    if (!router.isReady) return;
    
    // Extract webhook data from query parameters
    const { repauto, idcommande, montant, numTrans, carte, token } = router.query;
    
    // Validate required parameters
    if (!repauto || !idcommande) {
      // Redirect to failure page if required parameters are missing
      router.push('/payment/failed?error=Missing required parameters');
      return;
    }
    
    // Create webhook data object
    const data: WebhookData = {
      repauto: repauto as string,
      idcommande: idcommande as string,
      montant: parseFloat(montant as string) || 0,
      numTrans: numTrans as string,
      carte: carte as string,
      token: token as string,
    };
    
    // Set webhook data
    setWebhookData(data);
  }, [router.isReady, router.query, router]);
  
  return (
    <>
      <Head>
        <title>Traitement du Paiement | AtlasFit</title>
        <meta
          name="description"
          content="Traitement de votre paiement en cours..."
        />
      </Head>
      <Layout>
        {webhookData ? (
          <WebhookHandler webhookData={webhookData} />
        ) : (
          <div className="flex min-h-[50vh] items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
          </div>
        )}
      </Layout>
    </>
  );
}
