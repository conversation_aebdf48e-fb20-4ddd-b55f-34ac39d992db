import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { reference, status, redirectUrl } = req.body;

    // In production, verify the webhook signature here
    // You should validate the request is coming from your backend

    if (!reference || !status || !redirectUrl) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Here you could:
    // 1. Store the payment status in your database
    // 2. Update your application state
    // 3. Trigger emails or other notifications

    // For now, we'll just return the redirect URL
    return res.status(200).json({ 
      success: true,
      redirectUrl: `${redirectUrl}&webhook_received=true`
    });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}