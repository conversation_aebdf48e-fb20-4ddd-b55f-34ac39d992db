// hooks/usePaymentStatus.ts
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export function usePaymentStatus() {
  const router = useRouter();
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

  useEffect(() => {
    // Function to handle redirection
    const handleRedirect = (status: string, redirectUrl: string) => {
      setPaymentStatus(status);
      router.push(redirectUrl);
      localStorage.removeItem('webhookData');
    };

    // Check for webhook data in localStorage on mount
    const webhookData = localStorage.getItem('webhookData');
    if (webhookData) {
      const { status, redirectUrl } = JSON.parse(webhookData);
      handleRedirect(status, redirectUrl);
    }

    // Listen for storage events
    const handleStorage = (e: StorageEvent) => {
      if (e.key === 'webhookData' && e.newValue) {
        const { status, redirectUrl } = JSON.parse(e.newValue);
        handleRedirect(status, redirectUrl);
      }
    };

    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, [router]);

  return { paymentStatus };
}