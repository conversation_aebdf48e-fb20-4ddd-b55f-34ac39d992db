import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import AuthLayout from '@/components/auth/AuthLayout';
import LoginForm from '@/components/auth/LoginForm';

export default function Login() {
  const router = useRouter();
  const { data: session } = useSession();

  // If user is already logged in, redirect to home page
  if (session) {
    router.push('/');
    return null;
  }

  return (
    <AuthLayout 
      title="Se connecter" 
      description="Connectez-vous à votre compte AtlasFit pour accéder à nos services premium de fitness."
    >
      <LoginForm />
    </AuthLayout>
  );
}
