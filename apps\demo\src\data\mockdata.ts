// src/data/mockdata.ts
import { User } from "./interface";

// Load users from localStorage if available
const loadUsers = (): User[] => {
  return [
    {
      id: "4e732cf5-ea45-41a0-989f-f54bb7947c2f",
      name: "<PERSON>",
      email: "azhari<PERSON><EMAIL>",
      password: "azhariiom",
    },
    {
      id: "0f873fa4-3f37-49d0-8af4-867ae370ec18",
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "saadpayment",
    },
    {
      id: "453f08cd-42ec-46cb-9650-77a199f22297",
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "campusreussite",
    },
    {
      id: "9fcf21fb-858e-4010-9126-35edece15120",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      password: "abdelhadimaher5",
    },
    {
      id: "cdb7f6b6-18dc-4381-80ce-77c9fe12f7a5",
      name: "nbg",
      email: "<EMAIL>",
      password: "nbg123",
    },
  ];
};

// Initialize users
// eslint-disable-next-line prefer-const
export let MOCK_USERS: User[] = loadUsers();

// Function to add a new user
export const addUser = (newUser: User) => {
  // Check if user already exists
  const existingUser = MOCK_USERS.find((u) => u.email === newUser.email);
  if (existingUser) {
    throw new Error("User already exists");
  }

  // Add new user
  MOCK_USERS.push(newUser);

  // Save to localStorage if in browser
  if (typeof window !== "undefined") {
    localStorage.setItem("MOCK_USERS", JSON.stringify(MOCK_USERS));
  }
};

// Function to find a user
export const findUser = (email: string, password: string) => {
  return MOCK_USERS.find((u) => u.email === email && u.password === password);
};

export const findUserByEmail = (email: string) => {
  return MOCK_USERS.find((u) => u.email === email);
};
