// pages/api/payment/webhook.ts
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { reference, status } = req.query;
    console.log('Webhook received:', { reference, status });

    if (!reference || !status) {
      return res.redirect(303, `/payment/error?message=missing_parameters`);
    }

    // Determine which page to redirect to based on payment status
    let redirectDestination = '';
    
    if (status === 'SUCCESS') {
      // Redirect to success page with reference as query parameter
      redirectDestination = `/payment/success?reference=${reference}`;
    } else {
      // Redirect to failed page with reference and status as query parameters
      redirectDestination = `/payment/failed?reference=${reference}&status=${status}`;
    }

    // Perform the redirect without returning the response object
    res.redirect(303, redirectDestination);
    return;
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.redirect(303, `/payment/error?message=internal_server_error`);
    return;
  }
}