import { WebhookData } from '@/types/subscription';

// Hardcoded URLs for success and failure redirects
export const SUCCESS_URL = '/payment/success';
export const FAILURE_URL = '/payment/failed';

/**
 * Process webhook data and return the appropriate redirect URL
 * @param webhookData The webhook data from the payment service
 * @returns The URL to redirect the user to
 */
export const processWebhookRedirect = (webhookData: WebhookData): string => {
  // Check if the payment was successful
  const isSuccess = webhookData.repauto === '00';
  
  // Prepare query parameters for the redirect URL
  const queryParams = new URLSearchParams();
  
  if (webhookData.idcommande) {
    queryParams.append('reference', webhookData.idcommande);
  }
  
  if (webhookData.montant) {
    queryParams.append('amount', webhookData.montant.toString());
  }

  // Build the redirect URL with query parameters
  let redirectUrl = isSuccess ? SUCCESS_URL : FAILURE_URL;
  
  // Add query parameters if any exist
  if (queryParams.toString()) {
    redirectUrl += `?${queryParams.toString()}`;
  }

  // If payment failed, add error information
  if (!isSuccess && webhookData.repauto) {
    // Add error code to the redirect URL
    const errorQueryParams = new URLSearchParams(queryParams);
    errorQueryParams.append('error', `Code d'erreur: ${webhookData.repauto}`);
    redirectUrl = `${FAILURE_URL}?${errorQueryParams.toString()}`;
  }

  return redirectUrl;
};

/**
 * Handle the webhook response from the backend
 * @param response The webhook response from the backend
 * @returns void - redirects the user to the appropriate page
 */
export const handleWebhookResponse = (response: { redirectUrl: string }): void => {
  if (response && response.redirectUrl) {
    // Redirect to the URL provided by the backend
    window.location.href = response.redirectUrl;
  } else {
    // Fallback to the failure URL if no redirect URL is provided
    window.location.href = FAILURE_URL;
  }
};
