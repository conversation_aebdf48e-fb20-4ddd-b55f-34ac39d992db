import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import AuthLayout from '@/components/auth/AuthLayout';
import RegisterForm from '@/components/auth/RegisterForm';

export default function Register() {
  const router = useRouter();
  const { data: session } = useSession();

  // If user is already logged in, redirect to home page
  if (session) {
    router.push('/');
    return null;
  }

  return (
    <AuthLayout 
      title="S'inscrire" 
      description="Créez votre compte AtlasFit pour accéder à nos services premium de fitness."
    >
      <RegisterForm />
    </AuthLayout>
  );
}
