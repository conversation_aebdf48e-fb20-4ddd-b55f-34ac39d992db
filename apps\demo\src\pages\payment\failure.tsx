import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';

export default function PaymentFailed() {
  const router = useRouter();
  // const [countdown, setCountdown] = useState(10);

  // Get query parameters
  const { error, reference } = router.query;

  // Redirect to memberships after countdown
  // useEffect(() => {
  //   if (countdown > 0) {
  //     const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
  //     return () => clearTimeout(timer);
  //   } else {
  //     router.push('/#memberships');
  //   }
  // }, [countdown, router]);

  return (
    <>
      <Head>
        <title>Paiement Échoué | AtlasFit</title>
        <meta
          name="description"
          content="Votre paiement n'a pas pu être traité. Veuillez réessayer."
        />
      </Head>
      <Layout>
        <div className="bg-gray-50 py-16 sm:py-24">
          <Container>
            <div className="mx-auto max-w-3xl text-center">
              <div className="mb-8 flex justify-center">
                <div className="rounded-full bg-red-100 p-4">
                  <svg
                    className="h-12 w-12 text-red-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Paiement Échoué
              </h1>

              <p className="mt-4 text-lg text-gray-500">
                Nous n'avons pas pu traiter votre paiement. Veuillez vérifier vos informations de paiement et réessayer.
              </p>

              {(error || reference) && (
                <div className="mt-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 className="text-lg font-medium text-gray-900">Détails de l'erreur</h2>
                  <dl className="mt-4 grid grid-cols-1 gap-x-6 gap-y-4">
                    {error && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Message d'erreur</dt>
                        <dd className="mt-1 text-sm text-red-600">{error}</dd>
                      </div>
                    )}
                    {reference && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Référence</dt>
                        <dd className="mt-1 text-sm text-gray-900">{reference}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              )}

              {/* <div className="mt-8">
                <p className="text-sm text-gray-500">
                  Vous serez redirigé vers la page des abonnements dans {countdown} seconde{countdown !== 1 ? 's' : ''}.
                </p>
                <div className="mt-6 flex justify-center space-x-4">
                  <Button href="/#memberships" variant="primary" className="bg-red-600 hover:bg-red-700">
                    Réessayer
                  </Button>
                  <Button href="/" variant="outline">
                    Retour à l'accueil
                  </Button>
                </div>
              </div> */}

              <div className="mt-12 border-t border-gray-200 pt-8">
                <h2 className="text-lg font-medium text-gray-900">Besoin d'aide?</h2>
                <p className="mt-2 text-sm text-gray-500">
                  Si vous continuez à rencontrer des problèmes, veuillez contacter notre équipe de support à{' '}
                  <a href="mailto:<EMAIL>" className="font-medium text-red-600 hover:text-red-500">
                    <EMAIL>
                  </a>
                  {' '}ou appelez-nous au{' '}
                  <a href="tel:+212522123456" className="font-medium text-red-600 hover:text-red-500">
                    +212 522 123 456
                  </a>.
                </p>
              </div>
            </div>
          </Container>
        </div>
      </Layout>
    </>
  );
}
