import React from 'react';

interface AuthBackgroundProps {
  children: React.ReactNode;
}

export default function AuthBackground({ children }: AuthBackgroundProps) {
  return (
    <div className="relative min-h-[600px] overflow-hidden bg-gray-50 py-16 sm:py-24">
      {/* Background pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <svg
          className="absolute left-0 top-0 h-full w-full"
          width="100%"
          height="100%"
          viewBox="0 0 800 800"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <pattern
              id="pattern"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M0 20 L40 20 M20 0 L20 40"
                stroke="currentColor"
                strokeWidth="2"
              />
            </pattern>
          </defs>
          <rect width="800" height="800" fill="url(#pattern)" />
        </svg>
      </div>

      {/* Red accent circles */}
      <div
        className="absolute -left-20 -top-20 h-80 w-80 rounded-full bg-red-500/20 blur-3xl"
        aria-hidden="true"
      />
      <div
        className="absolute -bottom-20 -right-20 h-80 w-80 rounded-full bg-red-500/20 blur-3xl"
        aria-hidden="true"
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  );
}
