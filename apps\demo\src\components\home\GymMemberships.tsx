import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Container from '../ui/Container';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { subscribeUser } from '@/services/subscriptionService';
import { BillingInterval } from '@/types/subscription';
import { usePaymentStatus } from '@/hooks/usePaymentStatus';

type BillingFrequency = 'twominute' | 'halfhour' | 'daily';

interface MembershipFeature {
  name: string;
  included: boolean;
}

interface MembershipPlan {
  name: string;
  description: string;
  price: {
    twominute: number;
    halfhour: number;
    daily: number;
  };
  features: MembershipFeature[];
  cta: string;
  highlighted?: boolean;
}

export default function GymMemberships() {
  const router = useRouter();
  const { data: session } = useSession();
  const [frequency, setFrequency] = useState<BillingFrequency>('twominute');
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [paymentReference, setPaymentReference] = useState<string | null>(null);

  const { paymentStatus } = usePaymentStatus();

  useEffect(() => {
    if (paymentStatus === 'SUCCESS') {
      const reference = paymentReference || localStorage.getItem('paymentReference');
      router.push(`/payment/success?reference=${reference}`);
    } else if (paymentStatus === 'FAILED') {
      const reference = paymentReference || localStorage.getItem('paymentReference');
      router.push(`/payment/failed?reference=${reference}`);
    }
  }, [paymentStatus, paymentReference, router]);

  // Map BillingFrequency to BillingInterval enum
  const getBillingInterval = (freq: BillingFrequency): BillingInterval => {
    switch (freq) {
      case 'twominute':
        return BillingInterval.TWOMINUTE;
      case 'halfhour':
        return BillingInterval.HALFHOUR;
      case 'daily':
        return BillingInterval.DAILY;
      default:
        return BillingInterval.TWOMINUTE;
    }
  };

  // Handle subscription
  const handleSubscribe = async (plan: MembershipPlan) => {
    // If user is not logged in, redirect to login page
    if (!session) {
      router.push('/login');
      return;
    }

    try {
      setIsLoading(plan.name);

      // Create user data from session
      const userData = {
        externalId: session.user.id,
        email: session.user.email || '',
        username: session.user.name || '',
        authProvider: 'email'
      };

      // Get base URL for success/failure redirects
      const baseUrl = window.location.origin;

      // Subscribe user to plan
      const response = await subscribeUser(
        userData,
        plan.name,
        plan.price[frequency],
        getBillingInterval(frequency),
        baseUrl
      );

      // Redirect to payment URL
      if (response.url) {
        window.location.href = response.url;
      }
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      alert('Une erreur est survenue lors de la souscription. Veuillez réessayer.');
    } finally {
      setIsLoading(null);
    }
  };

  const plans: MembershipPlan[] = [
    {
      name: 'Basic',
      description: 'Idéal pour les débutants',
      price: {
        twominute: 10,
        halfhour: 50,
        daily: 299,
      },
      features: [
        { name: 'Accès à la salle de musculation', included: true },
        { name: 'Accès aux vestiaires', included: true },
        { name: 'Accès 5j/7 (lun-ven)', included: true },
        { name: 'Cours collectifs', included: false },
        { name: 'Accès à l\'espace cardio', included: true },
        { name: 'Coach personnel', included: false },
        { name: 'Accès à l\'application mobile', included: false },
      ],
      cta: 'Choisir Basic',
    },
    {
      name: 'Premium',
      description: 'Notre formule la plus populaire',
      price: {
        twominute: 20,
        halfhour: 100,
        daily: 499,
      },
      features: [
        { name: 'Accès à la salle de musculation', included: true },
        { name: 'Accès aux vestiaires', included: true },
        { name: 'Accès 7j/7', included: true },
        { name: 'Cours collectifs', included: true },
        { name: 'Accès à l\'espace cardio', included: true },
        { name: 'Coach personnel (1 séance/mois)', included: true },
        { name: 'Accès à l\'application mobile', included: true },
      ],
      cta: 'Choisir Premium',
      highlighted: true,
    },
    {
      name: 'Elite',
      description: 'Pour les sportifs exigeants',
      price: {
        twominute: 30,
        halfhour: 150,
        daily: 799,
      },
      features: [
        { name: 'Accès à la salle de musculation', included: true },
        { name: 'Accès aux vestiaires premium', included: true },
        { name: 'Accès 7j/7 (24h/24)', included: true },
        { name: 'Cours collectifs illimités', included: true },
        { name: 'Accès à l\'espace cardio', included: true },
        { name: 'Coach personnel (4 séances/mois)', included: true },
        { name: 'Accès à l\'application mobile', included: true },
      ],
      cta: 'Choisir Elite',
    },
  ];

  return (
    <div id="memberships" className="bg-gray-50 py-16 sm:py-24">
      <Container>
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Nos Formules d'Abonnement
          </h2>
          <p className="mx-auto mt-4 max-w-xl text-lg text-gray-500">
            Choisissez la formule qui correspond le mieux à vos besoins et objectifs sportifs.
          </p>
        </div>

        {/* Billing frequency toggle */}
        <div className="mt-12 flex justify-center">
          <div className="relative flex rounded-lg bg-gray-100 p-1">
            <button
              type="button"
              className={`relative w-32 rounded-md py-2 text-sm font-medium ${frequency === 'twominute'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
                }`}
              onClick={() => setFrequency('twominute')}
            >
              2 Minutes
            </button>
            <button
              type="button"
              className={`relative ml-0.5 w-32 rounded-md py-2 text-sm font-medium ${frequency === 'halfhour'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
                }`}
              onClick={() => setFrequency('halfhour')}
            >
              30 Minutes
            </button>
            <button
              type="button"
              className={`relative ml-0.5 w-32 rounded-md py-2 text-sm font-medium ${frequency === 'daily'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
                }`}
              onClick={() => setFrequency('daily')}
            >
              Journalier
            </button>
          </div>
        </div>

        {/* Membership plans */}
        <div className="mt-12 space-y-4 sm:mt-16 sm:grid sm:grid-cols-3 sm:gap-6 sm:space-y-0 xl:mx-auto xl:max-w-6xl">
          {plans.map((plan) => (
            <div key={plan.name} className="flex flex-col">
              <Card highlighted={plan.highlighted} className="flex flex-1 flex-col">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
                  <p className="mt-2 text-gray-500">{plan.description}</p>
                  <p className="mt-8">
                    <span className="text-4xl font-bold tracking-tight text-gray-900">
                      {plan.price[frequency]} MAD
                    </span>
                    <span className="text-base font-medium text-gray-500">
                      {frequency === 'twominute' ? '/2min' : frequency === 'halfhour' ? '/30min' : '/jour'}
                    </span>
                  </p>
                  <ul className="mt-8 space-y-4">
                    {plan.features.map((feature) => (
                      <li key={feature.name} className="flex items-start">
                        <div className="flex-shrink-0">
                          {feature.included ? (
                            <svg
                              className="h-5 w-5 text-green-500"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              aria-hidden="true"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                          ) : (
                            <svg
                              className="h-5 w-5 text-gray-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              aria-hidden="true"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                        <p
                          className={`ml-3 text-sm ${feature.included ? 'text-gray-700' : 'text-gray-400'
                            }`}
                        >
                          {feature.name}
                        </p>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-8">
                  <Button
                    onClick={() => handleSubscribe(plan)}
                    variant={plan.highlighted ? 'primary' : 'outline'}
                    className={`w-full justify-center ${plan.highlighted ? 'bg-red-600 hover:bg-red-700' : 'border-red-600 text-red-600 hover:bg-red-50'}`}
                    disabled={isLoading !== null}
                  >
                    {isLoading === plan.name ? 'Chargement...' : plan.cta}
                  </Button>
                </div>
              </Card>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
}
