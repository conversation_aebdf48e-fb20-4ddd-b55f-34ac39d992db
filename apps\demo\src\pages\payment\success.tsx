import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import Container from '@/components/ui/Container';
import { useSession } from 'next-auth/react';

export default function PaymentSuccess() {
  const router = useRouter();
  const { data: session } = useSession();
  const { reference, amount, plan } = router.query;

  return (
    <>
      <Head>
        <title>Paiement Réussi | AtlasFit</title>
        <meta
          name="description"
          content="Votre paiement a été traité avec succès. Bienvenue chez AtlasFit!"
        />
      </Head>
      <Layout>
        <div className="bg-gray-50 py-16 sm:py-24">
          <Container>
            <div className="mx-auto max-w-3xl text-center">
              <div className="mb-8 flex justify-center">
                <div className="rounded-full bg-green-100 p-4">
                  <svg
                    className="h-12 w-12 text-green-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Paiement Réussi!
              </h1>

              <p className="mt-4 text-lg text-gray-500">
                Merci pour votre abonnement à AtlasFit. Votre paiement a été traité avec succès.
              </p>

              {reference && (
                <div className="mt-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 className="text-lg font-medium text-gray-900">Détails de la transaction</h2>
                  <dl className="mt-4 grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Référence</dt>
                      <dd className="mt-1 text-sm text-gray-900">{reference}</dd>
                    </div>
                    {amount && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Montant</dt>
                        <dd className="mt-1 text-sm text-gray-900">{amount} MAD</dd>
                      </div>
                    )}
                    {plan && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Formule</dt>
                        <dd className="mt-1 text-sm text-gray-900">{plan}</dd>
                      </div>
                    )}
                    {session?.user?.email && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Email</dt>
                        <dd className="mt-1 text-sm text-gray-900">{session.user.email}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              )}

              {/* <div className="mt-8">
                <p className="text-sm text-gray-500">
                  Vous serez redirigé vers la page d'accueil dans {countdown} seconde{countdown !== 1 ? 's' : ''}.
                </p>
                <div className="mt-6 flex justify-center space-x-4">
                  <Button href="/" variant="primary">
                    Retour à l'accueil
                  </Button>
                  <Button href="/#memberships" variant="outline">
                    Voir les abonnements
                  </Button>
                </div>
              </div> */}
            </div>
          </Container>
        </div>
      </Layout>
    </>
  );
}
