import { findUser } from "../data/mockdata";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET || "AuthOptions-ONEDUSTRY",
  providers: [
    CredentialsProvider({
      id: 'Credentials',
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await findUser(credentials.email, credentials.password);

        if (user) {
          return {
            id: user.id.toString(),
            name: user.name,
            email: user.email
          };
        }
        return null;
      },
    })
  ],

  pages: {
    signIn: '/login',
    error: '/login',
  },

  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        // For Google provider, we need to use sub as the ID
        if (account.provider === 'google' && !user.id) {
          return {
            ...token,
            id: token.sub || user.id || crypto.randomUUID(),
          };
        }

        // For other providers, use the user ID
        return {
          ...token,
          id: user.id,
        };
      }

      // Return previous token if the user isn't new
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
}
