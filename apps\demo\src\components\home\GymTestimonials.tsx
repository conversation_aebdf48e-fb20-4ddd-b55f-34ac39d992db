import React from 'react'
import Container from '../ui/Container'

const testimonials = [
  {
    content: "J'ai rejoint AtlasFit il y a 6 mois et j'ai déjà perdu 15 kg. Les coachs sont très professionnels et l'ambiance est motivante. Je recommande vivement !",
    author: {
      name: "<PERSON><PERSON>",
      role: "Membre depuis 6 mois",
      imageUrl: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80"
    }
  },
  {
    content: "Les équipements sont de très haute qualité et les cours collectifs sont variés et dynamiques. J'adore particulièrement les cours de Zumba et de Spinning.",
    author: {
      name: "<PERSON><PERSON>",
      role: "Membre depuis 1 an",
      imageUrl: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80"
    }
  },
  {
    content: "En tant que sportif de haut niveau, j'ai besoin d'un suivi personnalisé. AtlasFit m'offre exactement ce dont j'ai besoin avec des coachs expérimentés.",
    author: {
      name: "Youssef El Amrani",
      role: "Athlète professionnel",
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80"
    }
  },
]

const GymTestimonials = () => {
  return (
    <div id="testimonials" className="bg-white py-16 sm:py-24">
      <Container>
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Ce que nos membres disent
          </h2>
          <p className="mt-4 text-lg text-gray-500">
            Découvrez les témoignages de nos membres qui ont transformé leur vie grâce à AtlasFit.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="group relative flex flex-col overflow-hidden rounded-2xl bg-white p-8 shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
              {/* Quote icon */}
              <div className="absolute -top-4 -left-4 text-red-100">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>

              <div className="relative flex-1 z-10">
                <div className="mb-6 flex items-center gap-x-2">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z" clipRule="evenodd" />
                    </svg>
                  ))}
                </div>

                <p className="mb-8 text-base italic text-gray-600 relative">"{testimonial.content}"</p>

                <div className="mt-auto pt-6 border-t border-gray-100 flex items-center gap-x-4">
                  <img
                    src={testimonial.author.imageUrl}
                    alt={testimonial.author.name}
                    className="h-14 w-14 rounded-full border-2 border-white shadow-md object-cover"
                  />
                  <div>
                    <h3 className="text-base font-bold leading-7 tracking-tight text-gray-900 group-hover:text-red-600 transition-colors">{testimonial.author.name}</h3>
                    <p className="text-sm font-medium leading-6 text-red-600">{testimonial.author.role}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Container>
    </div>
  )
}

export default GymTestimonials
