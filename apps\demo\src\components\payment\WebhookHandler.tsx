import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { WebhookData } from '@/types/subscription';
import { handleWebhookResponse } from '@/utils/paymentUtils';
import Container from '../ui/Container';

interface WebhookHandlerProps {
  webhookData: WebhookData;
}

export default function WebhookHandler({ webhookData }: WebhookHandlerProps) {
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const processWebhook = async () => {
      try {
        // Call the webhook API endpoint
        const response = await fetch('/api/payments/webhook', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookData),
        });
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Handle the webhook response
        handleWebhookResponse(data);
      } catch (error) {
        console.error('Error processing webhook:', error);
        setError('Une erreur est survenue lors du traitement du paiement.');
        setIsProcessing(false);
      }
    };
    
    if (webhookData) {
      processWebhook();
    }
  }, [webhookData]);
  
  if (error) {
    return (
      <Container>
        <div className="my-12 text-center">
          <div className="mb-6 flex justify-center">
            <div className="rounded-full bg-red-100 p-3">
              <svg
                className="h-8 w-8 text-red-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">Erreur de traitement</h2>
          <p className="mt-2 text-gray-600">{error}</p>
        </div>
      </Container>
    );
  }
  
  return (
    <Container>
      <div className="my-12 text-center">
        <div className="mb-6 flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Traitement du paiement</h2>
        <p className="mt-2 text-gray-600">Veuillez patienter pendant que nous traitons votre paiement...</p>
      </div>
    </Container>
  );
}
