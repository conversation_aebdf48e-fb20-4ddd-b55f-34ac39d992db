import React from 'react'
import Container from '../ui/Container'

const GymFeatures = () => {
	return (
		<div id="features" className="bg-white py-16 sm:py-24">
			<Container>
				<div className="mx-auto max-w-3xl text-center">
					<h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
						Pourquoi choisir AtlasFit ?
					</h2>
					<p className="mx-auto mt-4 max-w-xl text-lg text-gray-500">
						Notre salle de sport offre une expérience d'entraînement exceptionnelle avec des installations modernes et un service personnalisé.
					</p>
				</div>
				<div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
					{/* Caractéristique 1 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Équipements Premium</h3>
						<p className="mt-2 text-gray-500">
							Des machines de dernière génération importées d'Europe et des États-Unis pour un entraînement optimal.
						</p>
					</div>

					{/* Caractéristique 2 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Coachs Certifiés</h3>
						<p className="mt-2 text-gray-500">
							Une équipe de coachs professionnels certifiés internationalement pour vous accompagner dans votre parcours.
						</p>
					</div>

					{/* Caractéristique 3 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Cours Collectifs</h3>
						<p className="mt-2 text-gray-500">
							Plus de 20 cours collectifs par semaine : Zumba, Yoga, Pilates, Spinning, Boxe et bien plus encore.
						</p>
					</div>

					{/* Caractéristique 4 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Suivi Personnalisé</h3>
						<p className="mt-2 text-gray-500">
							Évaluation physique complète et programme d'entraînement sur mesure adapté à vos objectifs.
						</p>
					</div>

					{/* Caractéristique 5 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Horaires Flexibles</h3>
						<p className="mt-2 text-gray-500">
							Ouvert 7j/7 de 6h à 23h pour s'adapter à votre emploi du temps chargé.
						</p>
					</div>

					{/* Caractéristique 6 */}
					<div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-md bg-red-100 text-red-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
								<path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900">Boutique Sportive</h3>
						<p className="mt-2 text-gray-500">
							Accès à notre boutique de compléments alimentaires et d'équipements sportifs de qualité.
						</p>
					</div>
				</div>
			</Container>
		</div>
	)
}

export default GymFeatures
