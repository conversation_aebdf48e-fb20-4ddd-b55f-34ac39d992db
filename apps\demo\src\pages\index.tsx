import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import GymHero from '@/components/home/<USER>';
import GymFeatures from '@/components/home/<USER>';
import GymMemberships from '@/components/home/<USER>';
import GymClasses from '@/components/home/<USER>';
import GymTestimonials from '@/components/home/<USER>';
import GymCTA from '@/components/home/<USER>';

export default function Home() {
  return (
    <>
      <Head>
        <title>AtlasFit - Salle de Sport Premium au Maroc</title>
        <meta
          name="description"
          content="AtlasFit est une salle de sport premium au Maroc offrant des équipements modernes, des coachs certifiés et des programmes d'entraînement personnalisés."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Layout>
        {/* Hero Section */}
        <GymHero />

        {/* Features Section */}
        <GymFeatures />

        {/* Classes Section */}
        <GymClasses />

        {/* Memberships Section */}
        <GymMemberships />

        {/* Testimonials Section */}
        <GymTestimonials />

        {/* CTA Section */}
        <GymCTA />
      </Layout>
    </>
  );
}
