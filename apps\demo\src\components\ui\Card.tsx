import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  highlighted?: boolean;
}

export default function Card({ children, className = '', highlighted = false }: CardProps) {
  return (
    <div 
      className={`
        rounded-lg border ${highlighted ? 'border-blue-500 shadow-lg' : 'border-gray-200 shadow'} 
        bg-white p-6 transition-all ${highlighted ? 'scale-105' : ''}
        ${className}
      `}
    >
      {children}
    </div>
  );
}
